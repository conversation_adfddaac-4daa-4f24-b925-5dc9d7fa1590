import React, { createContext, ReactNode, useContext, useState } from 'react';
import { BASE_URL } from '../constants/Api';

/**
 * Navigation menu item interface
 */
export interface NavigationItem {
  id: string;
  titleKey: string;
  icon: string;
  url?: string;
  action?: () => void;
}

/**
 * Navigation state interface
 */
interface NavigationState {
  isSidebarOpen: boolean;
  currentRoute: string;
  navigationItems: NavigationItem[];
}

/**
 * Navigation context interface
 */
interface NavigationContextType extends NavigationState {
  toggleSidebar: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
  setCurrentRoute: (route: string) => void;
  getWebViewUrl: (route: string, token?: string) => string;
}

/**
 * Create Navigation Context
 */
const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

/**
 * Hook to use Navigation Context
 */
export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

/**
 * Navigation Provider Props
 */
interface NavigationProviderProps {
  children: ReactNode;
}

/**
 * Navigation Provider Component
 */
export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isSidebarOpen: false,
    currentRoute: 'dashboard',
    navigationItems: [
      {
        id: 'dashboard',
        titleKey: 'navigation.dashboard',
        icon: 'home',
        url: 'dashboard',
      },
      {
        id: 'fleet-management',
        titleKey: 'navigation.fleetManagement',
        icon: 'location-on',
        url: 'fleet-management',
      },
      {
        id: 'users',
        titleKey: 'navigation.userManagement',
        icon: 'people',
        url: 'users',
      },
      {
        id: 'drivers',
        titleKey: 'navigation.driverManagement',
        icon: 'person',
        url: 'drivers',
      },
      {
        id: 'vehicles',
        titleKey: 'navigation.vehicleManagement',
        icon: 'directions-car',
        url: 'vehicles',
      },
      {
        id: 'remote-control',
        titleKey: 'navigation.remoteControl',
        icon: 'wifi',
        url: 'remote-control',
      },
      {
        id: 'geofencing',
        titleKey: 'navigation.geofencing',
        icon: 'gps-fixed',
        url: 'geofencing',
      },
      {
        id: 'reporting',
        titleKey: 'navigation.reporting',
        icon: 'assessment',
        url: 'reporting',
      },
      {
        id: 'settings',
        titleKey: 'navigation.settings',
        icon: 'settings',
      },
      {
        id: 'logout',
        titleKey: 'navigation.logout',
        icon: 'logout',
      },
    ],
  });

  /**
   * Toggle sidebar open/close
   */
  const toggleSidebar = () => {
    setNavigationState(prev => ({
      ...prev,
      isSidebarOpen: !prev.isSidebarOpen,
    }));
  };

  /**
   * Close sidebar
   */
  const closeSidebar = () => {
    setNavigationState(prev => ({
      ...prev,
      isSidebarOpen: false,
    }));
  };

  /**
   * Open sidebar
   */
  const openSidebar = () => {
    setNavigationState(prev => ({
      ...prev,
      isSidebarOpen: true,
    }));
  };

  /**
   * Set current route
   */
  const setCurrentRoute = (route: string) => {
    setNavigationState(prev => ({
      ...prev,
      currentRoute: route,
    }));
  };

  /**
   * Get WebView URL for a route
   */
  const getWebViewUrl = (route: string, token?: string): string => {
    const baseUrl = `${BASE_URL}/${route}`;
    if (token) {
      return `${baseUrl}?token=${encodeURIComponent(token)}`;
    }
    return baseUrl;
  };

  /**
   * Context value
   */
  const contextValue: NavigationContextType = {
    ...navigationState,
    toggleSidebar,
    closeSidebar,
    openSidebar,
    setCurrentRoute,
    getWebViewUrl,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};
