import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { Image, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Navbar component props
 */
interface NavbarProps {
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
}

/**
 * Top navigation bar component
 */
export const Navbar: React.FC<NavbarProps> = ({
  onNotificationPress,
  onProfilePress,
}) => {
  const { toggleSidebar } = useNavigation();
  const { t } = useLanguage();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.navbar}>
        {/* Left side - Menu button */}
        <TouchableOpacity
          onPress={toggleSidebar}
          style={styles.iconButton}
          accessibilityLabel={t('navigation.menu')}
          accessibilityRole="button"
        >
          <MaterialIcons name="menu" size={24} color="#374151" />
        </TouchableOpacity>

        {/* Center - Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
            accessibilityLabel="ControllOne Logo"
          />
        </View>

        {/* Right side - Notification and Profile buttons */}
        <View style={styles.rightButtons}>
          {/* Notification button */}
          <TouchableOpacity
            onPress={onNotificationPress}
            style={styles.iconButton}
            accessibilityLabel={t('navigation.notifications')}
            accessibilityRole="button"
          >
            <MaterialIcons name="notifications" size={24} color="#374151" />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
  },
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 30,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconButton: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
  },
  logo: {
    width: 200,
    height: 60,
  },
  rightButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});
