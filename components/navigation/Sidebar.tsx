import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import {
  Dimensions,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { NavigationItem, useNavigation } from '../../contexts/NavigationContext';

const { width: screenWidth } = Dimensions.get('window');
const SIDEBAR_WIDTH = screenWidth * 0.8; // 80% of screen width

/**
 * Sidebar component props
 */
interface SidebarProps {
  onNavigate?: (route: string) => void;
}

/**
 * Sidebar menu item component
 */
interface SidebarItemProps {
  item: NavigationItem;
  isActive: boolean;
  onPress: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({ item, isActive, onPress }) => {
  const { t } = useLanguage();

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.sidebarItem,
        isActive ? styles.sidebarItemActive : styles.sidebarItemInactive,
      ]}
      accessibilityRole="button"
      accessibilityLabel={t(item.titleKey)}
    >
      <MaterialIcons
        name={item.icon as any}
        size={24}
        color={isActive ? '#FFFFFF' : '#6B7280'}
        style={styles.sidebarItemIcon}
      />
      <Text
        style={[
          styles.sidebarItemText,
          isActive ? styles.sidebarItemTextActive : styles.sidebarItemTextInactive,
        ]}
      >
        {t(item.titleKey)}
      </Text>
    </TouchableOpacity>
  );
};

/**
 * Sliding sidebar component
 */
export const Sidebar: React.FC<SidebarProps> = ({ onNavigate }) => {
  const {
    isSidebarOpen,
    closeSidebar,
    navigationItems,
    currentRoute,
    setCurrentRoute,
  } = useNavigation();
  const { t } = useLanguage();
  const { logout } = useAuth();

  /**
   * Handle navigation item press
   */
  const handleItemPress = async (item: NavigationItem) => {
    if (item.id === 'logout') {
      // Handle logout
      try {
        await logout();
        closeSidebar();
      } catch (error) {
        console.error('Logout error:', error);
      }
    } else if (item.id === 'settings') {
      // Settings - no action for now
      closeSidebar();
    } else if (item.url) {
      // Navigate to route
      setCurrentRoute(item.id);
      onNavigate?.(item.url);
      closeSidebar();
    }
  };

  // Split navigation items into main items and bottom items
  const mainItems = navigationItems.filter(
    item => !['settings', 'logout'].includes(item.id)
  );
  const bottomItems = navigationItems.filter(item =>
    ['settings', 'logout'].includes(item.id)
  );

  return (
    <Modal
      visible={isSidebarOpen}
      transparent
      animationType="none"
      onRequestClose={closeSidebar}
    >
      <View style={styles.modalContainer}>
        {/* Overlay */}
        <TouchableWithoutFeedback onPress={closeSidebar}>
          <View style={styles.overlay} />
        </TouchableWithoutFeedback>

        {/* Sidebar */}
        <View style={[styles.sidebar, { width: SIDEBAR_WIDTH }]}>
          {/* Header */}
          <View style={styles.header}>
            {/* Logo  */}
            <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/images/logo.png')}
                style={styles.logo}
                resizeMode="contain"
                accessibilityLabel="ControllOne Logo"
              />
            </View>

            {/* Close button */}
            <TouchableOpacity
              onPress={closeSidebar}
              style={styles.closeButton}
              accessibilityLabel={t('navigation.close')}
              accessibilityRole="button"
            >
              <MaterialIcons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>

          {/* Menu items */}
          <View style={styles.menuContainer}>
            <ScrollView
              style={styles.scrollView}
              showsVerticalScrollIndicator={false}
            >
              {/* Main navigation items */}
              {mainItems.map(item => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={currentRoute === item.id}
                  onPress={() => handleItemPress(item)}
                />
              ))}
            </ScrollView>

            {/* Bottom section */}
            <View style={styles.bottomSection}>
              {/* Settings and Logout */}
              {bottomItems.map(item => (
                <SidebarItem
                  key={item.id}
                  item={item}
                  isActive={false}
                  onPress={() => handleItemPress(item)}
                />
              ))}

              {/* Copyright */}
              <View style={styles.copyrightContainer}>
                <Text style={styles.copyrightText}>
                  {t('navigation.copyright')}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  overlay: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sidebar: {
    backgroundColor: '#ffffff',
    height: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 200,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  menuContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingVertical: 16,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
  },
  sidebarItemActive: {
    backgroundColor: '#F54619',
  },
  sidebarItemInactive: {
    backgroundColor: 'transparent',
  },
  sidebarItemIcon: {
    marginRight: 12,
  },
  sidebarItemText: {
    fontSize: 16,
    fontWeight: '500',
  },
  sidebarItemTextActive: {
    color: '#ffffff',
  },
  sidebarItemTextInactive: {
    color: '#374151',
  },
  bottomSection: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingVertical: 16,
  },
  copyrightContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  copyrightText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
});
