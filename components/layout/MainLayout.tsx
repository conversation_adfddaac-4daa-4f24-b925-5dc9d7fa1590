import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '../../contexts/NavigationContext';
import { Navbar } from '../navigation/Navbar';
import { Sidebar } from '../navigation/Sidebar';

/**
 * Main layout component props
 */
interface MainLayoutProps {
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
}

/**
 * Main layout component that combines navbar, sidebar, and content area
 */
export const MainLayout: React.FC<MainLayoutProps> = ({
  onNotificationPress,
  onProfilePress,
}) => {
  const { currentRoute, getWebViewUrl } = useNavigation();
  const { token } = useAuth();
  const [webViewUrl, setWebViewUrl] = useState<string>('');

  /**
   * Update WebView URL when route or token changes
   */
  useEffect(() => {
    if (token) {
      const url = getWebViewUrl(currentRoute, token);
      setWebViewUrl(url);
    }
  }, [currentRoute, token, getWebViewUrl]);

  /**
   * Handle navigation from sidebar
   */
  const handleNavigate = (route: string) => {
    if (token) {
      const url = getWebViewUrl(route, token);
      setWebViewUrl(url);
    }
  };

  /**
   * Handle notification press
   */
  const handleNotificationPress = () => {
    onNotificationPress?.();
    // TODO: Implement notification functionality
  };

  /**
   * Handle profile press
   */
  const handleProfilePress = () => {
    onProfilePress?.();
    // TODO: Implement profile functionality
  };

  return (
    <View style={styles.container}>
      {/* Navbar */}
      <Navbar
        onNotificationPress={handleNotificationPress}
        onProfilePress={handleProfilePress}
      />

      {/* Main content area */}
      <View style={styles.contentContainer}>
        {webViewUrl ? (
          <WebView
            source={{ uri: webViewUrl }}
            style={styles.webview}
            startInLoadingState={true}
            renderLoading={() => (
              <View style={styles.loader}>
                <ActivityIndicator size="large" color="#F54619" />
              </View>
            )}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error: ', nativeEvent);
            }}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView HTTP error: ', nativeEvent);
            }}
            onLoadStart={() => {
              console.log('WebView loading started for:', webViewUrl);
            }}
            onLoadEnd={() => {
              console.log('WebView loading ended for:', webViewUrl);
            }}
            // Allow navigation within the same domain
            onShouldStartLoadWithRequest={(request) => {
              const { url } = request;
              // Allow navigation within the ControllOne domain
              return url.includes('piattaforma.controllone.it');
            }}
            // Security settings
            javaScriptEnabled={true}
            domStorageEnabled={true}
            scalesPageToFit={true}
            bounces={false}
            scrollEnabled={true}
            // iOS specific settings
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            // Android specific settings
            mixedContentMode="compatibility"
            thirdPartyCookiesEnabled={true}
          />
        ) : (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#F54619" />
          </View>
        )}
      </View>

      {/* Sidebar */}
      <Sidebar onNavigate={handleNavigate} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  webview: {
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
  },
  loader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    zIndex: 10,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
